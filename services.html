<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Professional web design services including custom websites, UI/UX design, backend integration, and SEO optimization. Transform your digital presence with Sharp's Web Design."
    />
    <meta
      name="keywords"
      content="web design services, UI/UX design, custom websites, SEO optimization, backend integration, responsive design, e-commerce development"
    />
    <meta name="author" content="Sharp's Web Design" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Our Services - Sharp's Web Design" />
    <meta
      property="og:description"
      content="Comprehensive web design services including custom websites, UI/UX design, and SEO optimization for businesses."
    />
    <meta property="og:type" content="website" />

    <title>
      Our Services - Professional Web Design & Development | Sharp's Web Design
    </title>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <nav class="nav">
        <div class="logo">
          <a href="index.html">SwD<span>.</span></a>
        </div>
        <ul class="nav-menu">
          <li><a href="index.html" class="nav-link">Home</a></li>
          <li><a href="services.html" class="nav-link active">Services</a></li>
          <li><a href="about.html" class="nav-link">About</a></li>
          <li><a href="portfolio.html" class="nav-link">Portfolio</a></li>
          <li><a href="contact.html" class="nav-link">Contact</a></li>
        </ul>
        <div class="mobile-menu-toggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </nav>
    </header>

    <!-- Main Content -->
    <main>
      <!-- Hero Section -->
      <section class="page-hero">
        <div class="container">
          <div class="page-hero-content fade-in">
            <h1>Our <span class="highlight">Solutions</span></h1>
            <p>
              <span class="code-text">Custom-engineered</span> digital
              experiences designed for
              <span class="highlight">dominance</span> and conversion
              optimization
            </p>
          </div>
        </div>
      </section>

      <!-- Services Overview -->
      <section class="section">
        <div class="container">
          <div class="section-title fade-in">
            <h2>What We Offer</h2>
            <p>
              From concept to launch, we provide end-to-end web solutions
              tailored to your business needs
            </p>
          </div>
        </div>
      </section>

      <!-- Web Design Service -->
      <section id="web-design" class="service-detail section">
        <div class="container">
          <div class="service-content">
            <div class="service-text fade-in">
              <div class="service-icon-large">
                <svg
                  width="80"
                  height="80"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                  <line x1="8" y1="21" x2="16" y2="21"></line>
                  <line x1="12" y1="17" x2="12" y2="21"></line>
                </svg>
              </div>
              <h2>Custom Web Design</h2>
              <p class="service-description">
                We create stunning, unique websites that perfectly represent
                your brand and engage your target audience. Every design is
                crafted from scratch, ensuring your website stands out from the
                competition.
              </p>

              <!-- Inline mockup shown only on mobile between description and What's Included -->
              <div class="service-image-inline mobile-only fade-in">
                <div class="service-mockup">
                  <div class="mockup-browser">
                    <div class="browser-header">
                      <div class="browser-buttons">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <div class="browser-url">https://yourbusiness.com</div>
                    </div>
                    <div class="browser-content">
                      <div class="mockup-website">
                        <div class="mockup-header"></div>
                        <div class="mockup-content">
                          <div class="mockup-section">
                            <div class="mockup-section-title"></div>
                            <div class="mockup-text-line"></div>
                            <div class="mockup-text-line short"></div>
                            <div class="mockup-text-line medium"></div>
                          </div>
                          <div class="mockup-section">
                            <div class="mockup-section-title"></div>
                            <div class="mockup-text-line medium"></div>
                            <div class="mockup-text-line"></div>
                            <div class="mockup-text-line short"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="service-features">
                <h3>What's Included:</h3>
                <ul>
                  <li>Custom visual design tailored to your brand</li>
                  <li>Responsive design for all devices</li>
                  <li>User-friendly navigation and layout</li>
                  <li>Modern, professional aesthetics</li>
                  <li>Brand consistency across all pages</li>
                  <li>Fast loading optimization</li>
                </ul>
              </div>

              <div class="service-benefits">
                <h3>Benefits:</h3>
                <div class="benefits-grid">
                  <div class="benefit-item">
                    <h4>Standout Brand Presence</h4>
                    <p>
                      Distinct, on-brand visuals crafted specifically for your
                      business
                    </p>
                  </div>
                  <div class="benefit-item">
                    <h4>Conversion-Focused</h4>
                    <p>
                      Clear layouts and CTAs designed to turn visitors into
                      enquiries
                    </p>
                  </div>
                  <div class="benefit-item">
                    <h4>Mobile‑First &amp; Fast</h4>
                    <p>
                      Responsive design and performance tuning for speed on all
                      devices
                    </p>
                  </div>
                  <div class="benefit-item">
                    <h4>Future‑Proof</h4>
                    <p>
                      Clean, scalable build so updates and new pages are simple
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div class="service-image fade-in">
              <div class="service-mockup">
                <div class="mockup-browser">
                  <div class="browser-header">
                    <div class="browser-buttons">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                    <div class="browser-url">https://yourbusiness.com</div>
                  </div>
                  <div class="browser-content">
                    <div class="mockup-website">
                      <div class="mockup-header"></div>
                      <div class="mockup-content">
                        <div class="mockup-section">
                          <div class="mockup-section-title"></div>
                          <div class="mockup-text-line"></div>
                          <div class="mockup-text-line short"></div>
                          <div class="mockup-text-line medium"></div>
                        </div>
                        <div class="mockup-section">
                          <div class="mockup-section-title"></div>
                          <div class="mockup-text-line medium"></div>
                          <div class="mockup-text-line"></div>
                          <div class="mockup-text-line short"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- UI/UX Design Service -->
      <section id="ui-ux" class="service-detail section">
        <div class="container">
          <div class="service-content reverse">
            <div class="service-image fade-in">
              <div class="service-mockup">
                <div class="ux-mockup">
                  <div class="ux-wireframe">
                    <div class="wireframe-title"></div>
                    <div class="wireframe-header"></div>
                    <div class="wireframe-content">
                      <div class="wireframe-sidebar">
                        <div class="sidebar-item"></div>
                        <div class="sidebar-item"></div>
                        <div class="sidebar-item"></div>
                      </div>
                      <div class="wireframe-main">
                        <div class="wireframe-card"></div>
                        <div class="wireframe-card"></div>
                        <div class="wireframe-card"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="service-text fade-in">
              <div class="service-icon-large">
                <svg
                  width="80"
                  height="80"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                  <path d="M2 17l10 5 10-5"></path>
                  <path d="M2 12l10 5 10-5"></path>
                </svg>
              </div>
              <h2>UI/UX Design</h2>
              <p class="service-description">
                We design intuitive user interfaces and create seamless user
                experiences that guide visitors toward your business goals. Our
                designs focus on usability, accessibility, and conversion
                optimization.
              </p>

              <!-- Inline mockup for UI/UX shown only on mobile -->
              <div class="service-image-inline mobile-only fade-in">
                <div class="service-mockup">
                  <div class="ux-mockup">
                    <div class="ux-wireframe">
                      <div class="wireframe-title"></div>
                      <div class="wireframe-header"></div>
                      <div class="wireframe-content">
                        <div class="wireframe-sidebar">
                          <div class="sidebar-item"></div>
                          <div class="sidebar-item"></div>
                          <div class="sidebar-item"></div>
                        </div>
                        <div class="wireframe-main">
                          <div class="wireframe-card"></div>
                          <div class="wireframe-card"></div>
                          <div class="wireframe-card"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="service-features">
                <h3>What's Included:</h3>
                <ul>
                  <li>User research and persona development</li>
                  <li>Information architecture planning</li>
                  <li>Wireframing and prototyping</li>
                  <li>Interactive design elements</li>
                  <li>Accessibility compliance</li>
                  <li>Conversion rate optimization</li>
                </ul>
              </div>

              <div class="service-benefits">
                <h3>Benefits:</h3>
                <div class="benefits-grid">
                  <div class="benefit-item">
                    <h4>Increased Conversions</h4>
                    <p>Strategic design that guides users to take action</p>
                  </div>
                  <div class="benefit-item">
                    <h4>Better User Engagement</h4>
                    <p>Intuitive interfaces that keep visitors engaged</p>
                  </div>
                  <div class="benefit-item">
                    <h4>Reduced Bounce Rate</h4>
                    <p>Clear navigation and compelling content structure</p>
                  </div>
                  <div class="benefit-item">
                    <h4>Brand Trust</h4>
                    <p>Professional design builds credibility and trust</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- SEO Optimization Service -->
      <section id="seo" class="service-detail section">
        <div class="container">
          <div class="service-content">
            <div class="service-text fade-in">
              <div class="service-icon-large">
                <svg
                  width="80"
                  height="80"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="M21 21l-4.35-4.35"></path>
                  <path d="M11 6v10"></path>
                  <path d="M6 11h10"></path>
                </svg>
              </div>
              <h2>SEO Optimization</h2>
              <p class="service-description">
                We implement comprehensive SEO strategies to improve your
                website's visibility in search engines, drive organic traffic,
                and help potential customers find your business online.
              </p>

              <!-- Inline mockup for SEO shown only on mobile (positioned between description and What's Included) -->
              <div class="service-image-inline mobile-only fade-in">
                <div class="service-mockup">
                  <div class="seo-mockup">
                    <div class="seo-chart">
                      <div class="chart-header"></div>
                      <div class="chart-content">
                        <div class="chart-bars">
                          <div class="chart-bar"></div>
                          <div class="chart-bar"></div>
                          <div class="chart-bar"></div>
                          <div class="chart-bar"></div>
                          <div class="chart-bar"></div>
                        </div>
                      </div>
                      <div class="chart-labels">
                        <div class="chart-label">Jan</div>
                        <div class="chart-label">Feb</div>
                        <div class="chart-label">Mar</div>
                        <div class="chart-label">Apr</div>
                        <div class="chart-label">May</div>
                      </div>
                      <div class="seo-metrics">
                        <div class="seo-metric">
                          <div class="seo-metric-value">+150%</div>
                          <div class="seo-metric-label">Traffic</div>
                        </div>
                        <div class="seo-metric">
                          <div class="seo-metric-value">95</div>
                          <div class="seo-metric-label">Score</div>
                        </div>
                        <div class="seo-metric">
                          <div class="seo-metric-value">#1</div>
                          <div class="seo-metric-label">Ranking</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="service-features">
                <h3>What's Included:</h3>
                <ul>
                  <li>Keyword research and strategy</li>
                  <li>On-page SEO optimization</li>
                  <li>Technical SEO improvements</li>
                  <li>Meta tags and structured data</li>
                  <li>Site speed optimization</li>
                  <li>Local SEO (if applicable)</li>
                </ul>
              </div>

              <div class="seo-metrics">
                <h3>Expected Results:</h3>
                <div class="metrics-grid">
                  <div class="metric-item">
                    <div class="metric-number">150%</div>
                    <div class="metric-label">Increase in Organic Traffic</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-number">75%</div>
                    <div class="metric-label">
                      Improvement in Search Rankings
                    </div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-number">90%</div>
                    <div class="metric-label">Page Speed Score</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="service-image fade-in">
              <div class="service-mockup">
                <div class="seo-mockup">
                  <div class="seo-chart">
                    <div class="chart-header"></div>
                    <div class="chart-content">
                      <div class="chart-bars">
                        <div class="chart-bar"></div>
                        <div class="chart-bar"></div>
                        <div class="chart-bar"></div>
                        <div class="chart-bar"></div>
                        <div class="chart-bar"></div>
                      </div>
                    </div>
                    <div class="chart-labels">
                      <div class="chart-label">Jan</div>
                      <div class="chart-label">Feb</div>
                      <div class="chart-label">Mar</div>
                      <div class="chart-label">Apr</div>
                      <div class="chart-label">May</div>
                    </div>
                    <div class="seo-metrics">
                      <div class="seo-metric">
                        <div class="seo-metric-value">+150%</div>
                        <div class="seo-metric-label">Traffic</div>
                      </div>
                      <div class="seo-metric">
                        <div class="seo-metric-value">95</div>
                        <div class="seo-metric-label">Score</div>
                      </div>
                      <div class="seo-metric">
                        <div class="seo-metric-value">#1</div>
                        <div class="seo-metric-label">Ranking</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Backend Integration Service -->
      <section id="backend" class="service-detail section">
        <div class="container">
          <div class="service-content reverse">
            <div class="service-image fade-in">
              <div class="service-mockup">
                <div class="backend-mockup">
                  <div class="code-editor">
                    <div class="editor-header">
                      <div class="editor-tabs">
                        <span class="tab active">server.js</span>
                        <span class="tab">database.js</span>
                        <span class="tab">auth.js</span>
                      </div>
                      <div class="editor-status"></div>
                    </div>
                    <div class="editor-content">
                      <div class="code-line">
                        <span class="line-number">1</span>
                        <span class="code-text"
                          ><span class="comment"
                            >// Backend API Integration</span
                          ></span
                        >
                      </div>
                      <div class="code-line">
                        <span class="line-number">2</span>
                        <span class="code-text"
                          ><span class="keyword">const</span>
                          <span class="variable">express</span>
                          <span class="operator">=</span>
                          <span class="function">require</span
                          ><span class="punctuation">(</span
                          ><span class="string">'express'</span
                          ><span class="punctuation">);</span></span
                        >
                      </div>
                      <div class="code-line">
                        <span class="line-number">3</span>
                        <span class="code-text"
                          ><span class="keyword">const</span>
                          <span class="variable">app</span>
                          <span class="operator">=</span>
                          <span class="function">express</span
                          ><span class="punctuation">();</span></span
                        >
                      </div>
                      <div class="code-line">
                        <span class="line-number">4</span>
                        <span class="code-text"
                          ><span class="keyword">const</span>
                          <span class="variable">db</span>
                          <span class="operator">=</span>
                          <span class="function">require</span
                          ><span class="punctuation">(</span
                          ><span class="string">'./database'</span
                          ><span class="punctuation">);</span></span
                        >
                      </div>
                      <div class="code-line">
                        <span class="line-number">5</span>
                        <span class="code-text"></span>
                      </div>
                      <div class="code-line">
                        <span class="line-number">6</span>
                        <span class="code-text"
                          ><span class="variable">app</span
                          ><span class="punctuation">.</span
                          ><span class="function">post</span
                          ><span class="punctuation">(</span
                          ><span class="string">'/api/users'</span
                          ><span class="punctuation">,</span>
                          <span class="keyword">async</span>
                          <span class="punctuation">(</span
                          ><span class="variable">req</span
                          ><span class="punctuation">,</span>
                          <span class="variable">res</span
                          ><span class="punctuation">)</span>

                          <!-- Inline mockup for Backend shown only on mobile (positioned between description and What's Included) -->
                          <div class="service-image-inline mobile-only fade-in">
                            <div class="service-mockup">
                              <div class="backend-mockup">
                                <div class="code-editor">
                                  <div class="editor-header">
                                    <div class="editor-tabs">
                                      <span class="tab active">server.js</span>
                                      <span class="tab">database.js</span>
                                      <span class="tab">auth.js</span>
                                    </div>
                                    <div class="editor-status"></div>
                                  </div>
                                  <div class="editor-content">
                                    <div class="code-line">
                                      <span class="line-number">1</span
                                      ><span class="code-text"
                                        ><span class="comment"
                                          >// Backend API Integration</span
                                        ></span
                                      >
                                    </div>
                                    <div class="code-line">
                                      <span class="line-number">2</span
                                      ><span class="code-text"
                                        ><span class="keyword">const</span>
                                        <span class="variable">express</span>
                                        <span class="operator">=</span>
                                        <span class="function">require</span
                                        ><span class="punctuation">(</span
                                        ><span class="string">'express'</span
                                        ><span class="punctuation"
                                          >);</span
                                        ></span
                                      >
                                    </div>
                                    <div class="code-line">
                                      <span class="line-number">3</span
                                      ><span class="code-text"
                                        ><span class="keyword">const</span>
                                        <span class="variable">app</span>
                                        <span class="operator">=</span>
                                        <span class="function">express</span
                                        ><span class="punctuation"
                                          >();</span
                                        ></span
                                      >
                                    </div>
                                    <div class="code-line">
                                      <span class="line-number">4</span
                                      ><span class="code-text"
                                        ><span class="keyword">const</span>
                                        <span class="variable">db</span>
                                        <span class="operator">=</span>
                                        <span class="function">require</span
                                        ><span class="punctuation">(</span
                                        ><span class="string">'./database'</span
                                        ><span class="punctuation"
                                          >);</span
                                        ></span
                                      >
                                    </div>
                                    <div class="code-line">
                                      <span class="line-number">6</span
                                      ><span class="code-text"
                                        ><span class="variable">app</span
                                        ><span class="punctuation">.</span
                                        ><span class="function">post</span
                                        ><span class="punctuation">(</span
                                        ><span class="string">'/api/users'</span
                                        ><span class="punctuation">,</span>
                                        <span class="keyword">async</span>
                                        <span class="punctuation">(</span
                                        ><span class="variable">req</span
                                        ><span class="punctuation">,</span>
                                        <span class="variable">res</span
                                        ><span class="punctuation">)</span>
                                        <span class="operator">=></span>
                                        <span class="punctuation">{</span></span
                                      >
                                    </div>
                                    <div class="code-line">
                                      <span class="line-number">7</span
                                      ><span class="code-text"
                                        >&nbsp;&nbsp;<span class="keyword"
                                          >const</span
                                        >
                                        <span class="variable">user</span>
                                        <span class="operator">=</span>
                                        <span class="keyword">await</span>
                                        <span class="variable">db</span
                                        ><span class="punctuation">.</span
                                        ><span class="function">createUser</span
                                        ><span class="punctuation">(</span
                                        ><span class="variable">req</span
                                        ><span class="punctuation">.</span
                                        ><span class="variable">body</span
                                        ><span class="punctuation"
                                          >);</span
                                        ></span
                                      >
                                    </div>
                                    <div class="code-line">
                                      <span class="line-number">8</span
                                      ><span class="code-text"
                                        >&nbsp;&nbsp;<span class="variable"
                                          >res</span
                                        ><span class="punctuation">.</span
                                        ><span class="function">json</span
                                        ><span class="punctuation">(</span
                                        ><span class="variable">user</span
                                        ><span class="punctuation"
                                          >);</span
                                        ></span
                                      >
                                    </div>
                                    <div class="code-line">
                                      <span class="line-number">9</span
                                      ><span class="code-text"
                                        ><span class="punctuation"
                                          >});</span
                                        ></span
                                      >
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <span class="operator">=></span>
                          <span class="punctuation">{</span></span
                        >
                      </div>
                      <div class="code-line">
                        <span class="line-number">7</span>
                        <span class="code-text"
                          >&nbsp;&nbsp;<span class="keyword">const</span>
                          <span class="variable">user</span>
                          <span class="operator">=</span>
                          <span class="keyword">await</span>
                          <span class="variable">db</span
                          ><span class="punctuation">.</span
                          ><span class="function">createUser</span
                          ><span class="punctuation">(</span
                          ><span class="variable">req</span
                          ><span class="punctuation">.</span
                          ><span class="variable">body</span
                          ><span class="punctuation">);</span></span
                        >
                      </div>
                      <div class="code-line">
                        <span class="line-number">8</span>
                        <span class="code-text"
                          >&nbsp;&nbsp;<span class="variable">res</span
                          ><span class="punctuation">.</span
                          ><span class="function">json</span
                          ><span class="punctuation">(</span
                          ><span class="variable">user</span
                          ><span class="punctuation">);</span></span
                        >
                      </div>
                      <div class="code-line">
                        <span class="line-number">9</span>
                        <span class="code-text"
                          ><span class="punctuation">});</span></span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="service-text fade-in">
              <div class="service-icon-large">
                <svg
                  width="80"
                  height="80"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                  <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                  <line x1="6" y1="6" x2="6.01" y2="6"></line>
                  <line x1="6" y1="18" x2="6.01" y2="18"></line>
                </svg>
              </div>
              <h2>Backend Integration</h2>
              <p class="service-description">
                We develop robust backend systems and integrate third-party
                services to power your website's functionality, from contact
                forms to e-commerce platforms and custom applications.
              </p>

              <div class="service-features">
                <h3>What's Included:</h3>
                <ul>
                  <li>Custom API development</li>
                  <li>Database design and optimization</li>
                  <li>Third-party service integration</li>
                  <li>E-commerce functionality</li>
                  <li>User authentication systems</li>
                  <li>Content management systems</li>
                </ul>
              </div>

              <div class="integration-types">
                <h3>Popular Integrations:</h3>
                <div class="integration-grid">
                  <div class="integration-item">Payment Gateways</div>
                  <div class="integration-item">CRM Systems</div>
                  <div class="integration-item">Email Marketing</div>
                  <div class="integration-item">Analytics Tools</div>
                  <div class="integration-item">Social Media</div>
                  <div class="integration-item">Booking Systems</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Consultation Section -->
      <section class="section consultation-section">
        <div class="container">
          <div class="section-title fade-in">
            <h2>
              Let's Build Something <span class="highlight">Extraordinary</span>
            </h2>
            <p>
              Skip the packages. Let's discuss your vision and engineer a
              <span class="code-text">custom solution</span> that drives real
              results.
            </p>
          </div>

          <div class="consultation-content fade-in">
            <div class="consultation-intro">
              <h3>
                Why We Don't Do
                <span class="highlight">Cookie-Cutter</span> Solutions
              </h3>
              <p>
                Your business isn't like everyone else's—so why should your
                website be? We engineer
                <span class="highlight">bespoke digital experiences</span>
                tailored to your exact goals, audience, and growth strategy. No
                templates, no limitations, no compromises.
              </p>
              <div class="consultation-stats">
                <div class="stat-item">
                  <div class="stat-number">100%</div>
                  <div class="stat-label">Custom Built</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">24hr</div>
                  <div class="stat-label">Response Time</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">Free</div>
                  <div class="stat-label">Consultation</div>
                </div>
              </div>
            </div>

            <div class="consultation-options">
              <div class="consultation-card card">
                <div class="consultation-icon">📋</div>
                <h4>Complete Project Brief</h4>
                <p>
                  Fill out our comprehensive 8-section project brief form. This
                  detailed questionnaire covers everything from your business
                  goals to technical requirements, ensuring we understand your
                  vision completely.
                </p>
                <div class="option-benefits">
                  <span>✓ 8 comprehensive sections</span>
                  <span>✓ File uploads up to 10MB</span>
                  <span>✓ Detailed project analysis</span>
                  <span>✓ Custom proposal within 48hrs</span>
                </div>
                <a
                  href="https://docs.google.com/forms/d/e/1FAIpQLSd9BYhd4VooMs1CreiSEqs2RClK5gyT6ZLV_Kwky38kKqZw0Q/viewform?usp=dialog"
                  target="_blank"
                  class="btn btn-secondary"
                >
                  Open Project Brief Form
                </a>
              </div>

              <div class="consultation-card card featured-option">
                <div class="consultation-badge">Most Popular</div>
                <div class="consultation-icon">🚀</div>
                <h4>Strategy Call</h4>
                <p>
                  <strong>30-minute deep dive</strong> into your business goals,
                  target audience, and growth objectives. Walk away with
                  actionable insights even if we don't work together.
                </p>
                <div class="option-benefits">
                  <span>✓ Free consultation</span>
                  <span>✓ Instant calendar booking</span>
                  <span>✓ Same-day availability</span>
                </div>
                <button class="btn btn-primary" onclick="openCalendly()">
                  Book Strategy Call
                </button>
              </div>

              <div class="consultation-card card">
                <div class="consultation-icon">📞</div>
                <h4>Direct Connect</h4>
                <p>
                  Prefer direct communication? Call, text, or email for
                  immediate assistance with your project questions, timeline,
                  and budget discussions.
                </p>
                <div class="option-benefits">
                  <span>✓ Immediate response</span>
                  <span>✓ Call, text, or email</span>
                  <span>✓ Personal consultation</span>
                </div>
                <div class="direct-connect-options">
                  <a href="tel:+***********" class="btn btn-outline btn-small">
                    📞 Call/Text
                  </a>
                  <a
                    href="mailto:<EMAIL>?subject=Direct Project Inquiry&body=Hi! I'd like to discuss a web design project directly.%0D%0A%0D%0AProject Type: %0D%0ABusiness: %0D%0ATimeline: %0D%0ABudget Range: %0D%0A%0D%0AQuestions:%0D%0A%0D%0AThanks!"
                    class="btn btn-outline btn-small"
                  >
                    📧 Email
                  </a>
                </div>
              </div>
            </div>

            <div class="consultation-process">
              <h3>
                From <span class="highlight">Conversation</span> to
                <span class="code-text">Launch</span>
              </h3>
              <p class="process-intro">
                Our streamlined process ensures you get exactly what you need,
                when you need it.
              </p>

              <div class="process-steps">
                <div class="process-step">
                  <div class="step-number">1</div>
                  <div class="step-content">
                    <h4>Kickoff &amp; Brief</h4>
                    <p>
                      We confirm goals, audience, colours, pages, and features.
                      We collect ABN/ACN, contacts, domain, and brand assets—and
                      ask smart follow-ups so nothing gets missed.
                    </p>
                    <div class="step-duration">Form / Call / 30-min Zoom</div>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">2</div>
                  <div class="step-content">
                    <h4>Design &amp; Build</h4>
                    <p>
                      We get to work immediately and share a private preview
                      once the core is in place; you tell us what you love and
                      what to change, and we refine together until it’s
                      pixel-perfect and exactly what you want.
                    </p>
                    <div class="step-duration">Action over wireframes</div>
                  </div>
                </div>
                <div class="process-step">
                  <div class="step-number">3</div>
                  <div class="step-content">
                    <h4>Launch &amp; Ongoing Care</h4>
                    <p>
                      We publish your site, connect domain &amp; SSL, optimise
                      speed and on-page SEO, wire forms/bookings, and set
                      redirects &amp; analytics. You own your domain and
                      content—no lock-in. Need changes? Send them anytime;
                      typical turnaround 1–2 business days.
                    </p>
                    <div class="step-duration">Live, secure, supported</div>
                  </div>
                </div>
              </div>

              <div class="process-guarantee">
                <div class="guarantee-content">
                  <h4>💎 Premium Results, Rapid Delivery</h4>
                  <p>
                    We move fast and ship flawlessly—premium design and
                    engineered performance, zero compromises.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="section cta-section">
        <div class="container">
          <div class="cta-content fade-in text-center">
            <h2>Ready to Get Started?</h2>
            <p>
              Let's discuss your project and create a custom solution that fits
              your needs and budget.
            </p>
            <div class="cta-buttons">
              <a href="contact.html" class="btn btn-primary"
                >Start Your Project</a
              >
              <a href="portfolio.html" class="btn btn-secondary"
                >View Our Work</a
              >
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <div class="logo">SwD<span>.</span></div>
            <p>
              Engineering digital
              <span class="highlight">dominance</span> through custom-built,
              performance-optimized web experiences.
            </p>
            <div class="social-links">
              <a href="#" aria-label="Facebook">📘</a>
              <a href="#" aria-label="Twitter">🐦</a>
              <a href="#" aria-label="LinkedIn">💼</a>
              <a href="#" aria-label="Instagram">📷</a>
            </div>
          </div>
          <div class="footer-section">
            <h4>Services</h4>
            <ul>
              <li><a href="services.html#web-design">Web Design</a></li>
              <li><a href="services.html#ui-ux">UI/UX Design</a></li>
              <li><a href="services.html#seo">SEO Optimization</a></li>
              <li><a href="services.html#backend">Backend Integration</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Company</h4>
            <ul>
              <li><a href="about.html">About Us</a></li>
              <li><a href="portfolio.html">Portfolio</a></li>
              <li><a href="contact.html">Contact</a></li>
              <li><a href="services.html">Services</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Contact Info</h4>
            <div class="contact-info">
              <p>
                📧 <span data-email="contact"><EMAIL></span>
              </p>
              <p class="booking-consultation" onclick="openCalendly()">
                📅
                <span
                  style="
                    cursor: pointer;
                    color: var(--accent-cyan);
                    text-decoration: underline;
                  "
                  >Book Consultation</span
                >
              </p>
              <p>📞 0459 437 764</p>
              <p>📍 Sydney, NSW, Australia</p>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>
            &copy; 2025 SwD - Sharpy's Web Designs.
            <span class="highlight">Engineered</span> for dominance.
          </p>
        </div>
      </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>
  </body>
</html>
