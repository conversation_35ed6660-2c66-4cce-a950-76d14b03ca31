# World-Class 300-Point Backend Technical SEO & Performance Checklist (SwD)

This is the master checklist the automation enforces or can be extended to enforce. Items are grouped; many are validated in scripts/validate-*.js or via CI jobs.

1–25 Indexing & Meta Baseline
26–50 Canonicals & URL Canonicalization
51–80 Robots/Sitemaps (Pages & Images)
81–140 Structured Data (Core & Page-specific)
141–190 Performance Delivery (Images/Fonts/JS/CSS)
191–230 Security & Headers
231–270 Accessibility & Crawl Health
271–300 CI/CD, Monitoring & Regression Guards

Note: Many are already automated and enforced; the remainder are tracked here and can be added on request. This list is tailored to SwD and your constraints (free stack, no visual changes).

