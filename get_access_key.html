<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Web3Forms Access Key</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
            border: 1px solid #3bf0e4;
        }
        h1 {
            color: #3bf0e4;
            text-align: center;
        }
        input[type="email"] {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #3bf0e4;
            border-radius: 5px;
            background: #1a1a1a;
            color: white;
            font-size: 16px;
        }
        button {
            width: 100%;
            padding: 15px;
            background: #3bf0e4;
            color: #1a1a1a;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background: #2dd4c7;
        }
        .instructions {
            background: #333;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background: #2d5a27;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
        .error {
            background: #5a2727;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Get Your Web3Forms Access Key</h1>
        
        <div class="instructions">
            <h3>Step 1: Get Access Key</h3>
            <p>Enter your email below to get a free Web3Forms access key:</p>
        </div>

        <form id="keyForm">
            <input type="email" id="email" placeholder="<EMAIL>" value="<EMAIL>" required>
            <button type="submit" id="submitBtn">Get Access Key</button>
        </form>

        <div id="success" class="success">
            <h3>✅ Success!</h3>
            <p>Access key has been sent to your email. Check your Gmail inbox!</p>
            <p><strong>Next:</strong> Copy the access key from your email and update line 165 in script.js</p>
        </div>

        <div id="error" class="error">
            <h3>❌ Error</h3>
            <p>Failed to get access key. Please try again or go directly to <a href="https://web3forms.com/" target="_blank">web3forms.com</a></p>
        </div>

        <div class="instructions">
            <h3>Step 2: Update Your Code</h3>
            <p>Once you get the access key from your email:</p>
            <ol>
                <li>Open <code>script.js</code></li>
                <li>Find line 165: <code>formData.append("access_key", "YOUR_WEB3FORMS_KEY");</code></li>
                <li>Replace <code>"YOUR_WEB3FORMS_KEY"</code> with your actual access key</li>
                <li>Save the file</li>
                <li>Test your contact form!</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('keyForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const submitBtn = document.getElementById('submitBtn');
            const successDiv = document.getElementById('success');
            const errorDiv = document.getElementById('error');
            
            // Reset displays
            successDiv.style.display = 'none';
            errorDiv.style.display = 'none';
            
            // Show loading
            submitBtn.textContent = 'Getting Access Key...';
            submitBtn.disabled = true;
            
            try {
                const formData = new FormData();
                formData.append('email', email);
                
                const response = await fetch('https://web3forms.com/api/register', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    successDiv.style.display = 'block';
                } else {
                    throw new Error('Failed to register');
                }
            } catch (error) {
                console.error('Error:', error);
                errorDiv.style.display = 'block';
            } finally {
                submitBtn.textContent = 'Get Access Key';
                submitBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
